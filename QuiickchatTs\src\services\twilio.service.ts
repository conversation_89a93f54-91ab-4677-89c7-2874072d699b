import twilio from 'twilio';
import { ExternalServiceError, ConfigError } from '@/utils/errors';
import { logger } from '@/utils/logger';

const config = {
  accountSid: process.env.TWILIO_ACCOUNT_SID || '',
  authToken: process.env.TWILIO_AUTH_TOKEN || '',
  fromNumber: process.env.TWILIO_PHONE_NUMBER || '',
  enabled: process.env.TWILIO_ENABLED !== 'false'
};

const isEnabled = !!config.enabled && !!config.accountSid && !!config.authToken && !!config.fromNumber;
const client = isEnabled ? twilio(config.accountSid, config.authToken) : null;

if (isEnabled) {
  logger.info('Twilio service initialized successfully');
} else {
  logger.info('Twilio service is disabled or not configured');
}

export const sendOtp = async (phoneNumber: string, code: string): Promise<void> => {
  if (!isEnabled) {
    logger.info(`SMS is disabled. Verification code for ${phoneNumber}: ${code}`);
    return;
  }

  if (!client) {
    throw new ConfigError('Twilio service not properly configured');
  }

  try {
    const message = `Your QuiickChat verification code is: ${code}`;
    logger.info(`Sending SMS to ${phoneNumber}`);

    const result = await client.messages.create({
      body: message,
      from: config.fromNumber,
      to: phoneNumber
    });

    logger.info(`SMS sent successfully. SID: ${result.sid}`);
  } catch (error) {
    logger.error(`Failed to send SMS to ${phoneNumber}:`, error);
    throw new ExternalServiceError(`Failed to send SMS: ${error}`);
  }
};

export const sendCustomMessage = async (phoneNumber: string, message: string): Promise<void> => {
  if (!isEnabled) {
    logger.info(`SMS is disabled. Would have sent message to ${phoneNumber}: ${message}`);
    return;
  }

  if (!client) {
    throw new ConfigError('Twilio service not properly configured');
  }

  try {
    logger.info(`Sending custom SMS to ${phoneNumber}`);

    const result = await client.messages.create({
      body: message,
      from: config.fromNumber,
      to: phoneNumber
    });

    logger.info(`Custom SMS sent successfully. SID: ${result.sid}`);
  } catch (error) {
    logger.error(`Failed to send custom SMS to ${phoneNumber}:`, error);
    throw new ExternalServiceError(`Failed to send SMS: ${error}`);
  }
};

export const isServiceEnabled = (): boolean => {
  return isEnabled;
};

export const getServiceStatus = (): { enabled: boolean; configured: boolean } => {
  return {
    enabled: !!config.enabled,
    configured: !!(config.accountSid && config.authToken && config.fromNumber)
  };
};
