import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  getServiceStatus,
  getUserOnlineStatus,
  getUserInfo,
  registerCurrentUser,
  modifyUserInfo,
  addFriends,
  sendFriendRequests,
  deleteFriends,
  deleteAllFriends,
  queryFriendList,
  updateFriendsAlias,
  updateFriendAttributes,
  blockUsers,
  unblockUsers,
  queryBlocklist,
  checkBlockship
} from '@/controllers/zengo.controller';
import { authenticateRequest } from '@/middleware/auth.middleware';

export async function zengoRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/status', getServiceStatus as any);
  fastify.get('/users/online-status', { preHandler: authenticateRequest }, getUserOnlineStatus as any);
  fastify.get('/users/info', { preHandler: authenticateRequest }, getUserInfo as any);
  fastify.post('/register', { preHandler: authenticateRequest }, registerCurrentUser as any);
  fastify.post('/users/modify', { preHandler: authenticateRequest }, modifyUserInfo as any);
  fastify.post('/friends/add', { preHandler: authenticateRequest }, addFriends as any);
  fastify.post('/friends/request', { preHandler: authenticateRequest }, sendFriendRequests as any);
  fastify.post('/friends/delete', { preHandler: authenticateRequest }, deleteFriends as any);
  fastify.post('/friends/delete-all', { preHandler: authenticateRequest }, deleteAllFriends as any);
  fastify.get('/friends/list', { preHandler: authenticateRequest }, queryFriendList as any);
  fastify.post('/friends/update-alias', { preHandler: authenticateRequest }, updateFriendsAlias as any);
  fastify.post('/friends/update-attributes', { preHandler: authenticateRequest }, updateFriendAttributes as any);
  fastify.post('/users/block', { preHandler: authenticateRequest }, blockUsers as any);
  fastify.post('/users/unblock', { preHandler: authenticateRequest }, unblockUsers as any);
  fastify.get('/users/blocklist', { preHandler: authenticateRequest }, queryBlocklist as any);
  fastify.post('/users/check-blockship', { preHandler: authenticateRequest }, checkBlockship as any);
}
