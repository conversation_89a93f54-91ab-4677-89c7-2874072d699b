import fastify from 'fastify';
import cors from '@fastify/cors';
import multipart from '@fastify/multipart';

import dotenv from 'dotenv';
import { connectDatabase } from '@/config/database';
import { authRoutes } from '@/routes/auth.route';
import { userRoutes } from '@/routes/user.route';
import { contactRoutes } from '@/routes/contact.route';
import { statusRoutes } from '@/routes/status.route';
import { zengoRoutes } from '@/routes/zengo.route';

import { errorHandler } from '@/middleware/error.middleware';
import { setupWebSocket } from '@/services/websocket.service';
import { logger } from '@/utils/logger';


dotenv.config({ path: '.env' });

const createServer = () => {
  const loggerConfig: any = {
    level: process.env.LOG_LEVEL || 'info'
  };

  if (process.env.NODE_ENV === 'development') {
    loggerConfig.transport = {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname'
      }
    };
  }

  return fastify({
    logger: loggerConfig
  });
};

const registerPlugins = async (server: any) => {
  await server.register(cors, {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: process.env.CORS_CREDENTIALS === 'true'
  });

  await server.register(multipart);

};

const registerMiddleware = async (server: any) => {
  await server.register(errorHandler);
};

const registerRoutes = async (server: any) => {
  await server.register(authRoutes, { prefix: '/api/v1/auth' });
  await server.register(userRoutes, { prefix: '/api/v1/users' });
  await server.register(contactRoutes, { prefix: '/api/v1/contacts' });
  await server.register(statusRoutes, { prefix: '/api/v1/status' });
  await server.register(zengoRoutes, { prefix: '/api/v1/z' });
  await setupWebSocket(server);

  server.get('/health', async () => {
    return {
      success: true,
      message: 'Server is healthy',
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }
    };
  });
};

const startServer = async () => {
  const server = createServer();

  try {
    await registerPlugins(server);
    await connectDatabase();
    await registerMiddleware(server);
    await registerRoutes(server);

    const port = parseInt(process.env.PORT || '3000');
    const host = process.env.HOST || '0.0.0.0';

    await server.listen({ port, host });
    logger.info(`Server running on http://${host}:${port}`);

    return server;
  } catch (err) {
    console.error('Error starting server:', err);
    logger.error('Error starting server:', err);
    process.exit(1);
  }
};

const setupGracefulShutdown = (server: any) => {
  process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...');
    await server.close();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...');
    await server.close();
    process.exit(0);
  });
};

const main = async () => {
  const server = await startServer();
  setupGracefulShutdown(server);
};

main().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});
