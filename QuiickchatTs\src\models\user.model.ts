import mongoose, { Schema, Document } from 'mongoose';

export interface ICallRecord {
  contact_id: string;
  call_type: 'audio' | 'video';
  duration_seconds: number;
  timestamp: Date;
  status: 'completed' | 'missed' | 'declined';
}

export interface IVideoRecord {
  contact_id: string;
  duration_seconds: number;
  timestamp: Date;
  quality: string;
}

export interface IChatRecord {
  contact_id: string;
  last_message: string;
  last_message_timestamp: Date;
  unread_count: number;
}

export interface ITokenRecord {
  token_id: string;
  token_type: 'access' | 'refresh';
  expires_at: Date;
  created_at: Date;
  revoked: boolean;
}

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  email?: string;
  username?: string;
  phone: string;
  profile_picture?: string;
  is_verified: boolean;
  last_seen?: Date;
  contacts: string[];
  blocked_users: string[];
  status?: string;
  calls: ICallRecord[];
  videos: IVideoRecord[];
  chats: IChatRecord[];
  tokens: ITokenRecord[];
  bio?: string;
  address?: string;
  identity_key?: string;
  signed_pre_key?: string;
  pre_key?: string;
  one_time_keys: string[];
  last_key_update?: number;
  device_id?: string;
  registration_id?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const CallRecordSchema = new Schema<ICallRecord>({
  contact_id: { type: String, required: true },
  call_type: { type: String, enum: ['audio', 'video'], required: true },
  duration_seconds: { type: Number, required: true },
  timestamp: { type: Date, required: true, default: Date.now },
  status: { type: String, enum: ['completed', 'missed', 'declined'], required: true }
});

const VideoRecordSchema = new Schema<IVideoRecord>({
  contact_id: { type: String, required: true },
  duration_seconds: { type: Number, required: true },
  timestamp: { type: Date, required: true, default: Date.now },
  quality: { type: String, required: true }
});

const ChatRecordSchema = new Schema<IChatRecord>({
  contact_id: { type: String, required: true },
  last_message: { type: String, required: true },
  last_message_timestamp: { type: Date, required: true, default: Date.now },
  unread_count: { type: Number, default: 0 }
});

const TokenRecordSchema = new Schema<ITokenRecord>({
  token_id: { type: String, required: true },
  token_type: { type: String, enum: ['access', 'refresh'], required: true },
  expires_at: { type: Date, required: true },
  created_at: { type: Date, required: true, default: Date.now },
  revoked: { type: Boolean, default: false }
});

const UserSchema = new Schema<IUser>({
  email: { type: String, sparse: true },
  username: { type: String, sparse: true },
  phone: { type: String, required: true, unique: true, index: true },
  profile_picture: { type: String },
  is_verified: { type: Boolean, default: false },
  last_seen: { type: Date, default: Date.now },
  contacts: [{ type: String }],
  blocked_users: [{ type: String }],
  status: { type: String },
  calls: [CallRecordSchema],
  videos: [VideoRecordSchema],
  chats: [ChatRecordSchema],
  tokens: [TokenRecordSchema],
  bio: { type: String },
  address: { type: String },
  identity_key: { type: String },
  signed_pre_key: { type: String },
  pre_key: { type: String },
  one_time_keys: [{ type: String }],
  last_key_update: { type: Number },
  device_id: { type: String },
  registration_id: { type: String }
}, {
  timestamps: true
});

export const User = mongoose.model<IUser>('User', UserSchema);
