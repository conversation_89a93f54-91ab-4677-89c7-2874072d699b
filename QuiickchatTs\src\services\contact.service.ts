import { UserContacts, IContactItem } from '@/models/contact.model';
import { User } from '@/models/user.model';
import { DatabaseError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import { 
  ContactInput, 
  ContactResponse, 
  BulkContactsResponse, 
  ContactSyncResponse 
} from '@/types';
import mongoose from 'mongoose';

export const normalizePhoneNumber = (phone: string): string => {
  return phone.replace(/[^\d+]/g, '');
};

export const checkRegistration = async (normalizedPhone: string): Promise<any> => {
  try {
    const user = await User.findOne({ phone: normalizedPhone });
    return user;
  } catch (error) {
    logger.error('Failed to check registration:', error);
    throw new DatabaseError(`Failed to check registration: ${error}`);
  }
};

export const uploadContacts = async (userId: string, contacts: ContactInput[]): Promise<BulkContactsResponse> => {
  try {
    const now = new Date();
    const processedContacts: ContactResponse[] = [];
    let registeredCount = 0;
    let newContactsCount = 0;
    let updatedContactsCount = 0;

    let userContacts = await UserContacts.findOne({ user_id: userId });

    if (!userContacts) {
      userContacts = new UserContacts({
        user_id: userId,
        contacts: [],
        total_contacts: 0,
        registered_contacts: 0
      });
    }

    for (const contactInput of contacts) {
      const normalizedPhone = normalizePhoneNumber(contactInput.phone_number);
      
      if (!normalizedPhone) {
        continue;
      }

      const registeredUser = await checkRegistration(normalizedPhone);
      const isRegistered = !!registeredUser;
      const registeredUserId = registeredUser?._id?.toString();

      if (isRegistered) {
        registeredCount++;
      }

      const existingContactIndex = userContacts.contacts.findIndex(
        c => c.normalized_phone === normalizedPhone
      );

      const contactId = existingContactIndex >= 0 && userContacts.contacts[existingContactIndex]
        ? userContacts.contacts[existingContactIndex].contact_id
        : new mongoose.Types.ObjectId().toString();

      if (existingContactIndex >= 0 && userContacts.contacts[existingContactIndex]) {
        const existingContact = userContacts.contacts[existingContactIndex];
        existingContact.display_name = contactInput.display_name;
        existingContact.phone_number = contactInput.phone_number;
        existingContact.is_registered = isRegistered;
        existingContact.registered_user_id = registeredUserId;
        existingContact.updated_at = now;
        updatedContactsCount++;
      } else {
        const newContact: IContactItem = {
          contact_id: contactId,
          display_name: contactInput.display_name,
          phone_number: contactInput.phone_number,
          normalized_phone: normalizedPhone,
          is_registered: isRegistered,
          registered_user_id: registeredUserId,
          added_at: now,
          updated_at: now
        };
        userContacts.contacts.push(newContact);
        newContactsCount++;
      }

      const contactResponse: ContactResponse = {
        contact_id: contactId,
        display_name: contactInput.display_name,
        phone_number: contactInput.phone_number,
        is_registered: isRegistered,
        registered_user_id: registeredUserId,
        profile_picture: registeredUser?.profile_picture,
        status: registeredUser?.status,
        last_seen: registeredUser?.last_seen,
        added_at: now,
        updated_at: now
      };

      processedContacts.push(contactResponse);
    }

    userContacts.total_contacts = userContacts.contacts.length;
    userContacts.registered_contacts = userContacts.contacts.filter(c => c.is_registered).length;

    await userContacts.save();

    logger.info(`User ${userId} uploaded ${processedContacts.length} contacts (${newContactsCount} new, ${updatedContactsCount} updated, ${registeredCount} registered)`);

    const message = `${processedContacts.length} contacts processed successfully`;

    return {
      success: true,
      message,
      contacts_processed: processedContacts.length,
      contacts_registered: registeredCount,
      contacts: processedContacts
    };
  } catch (error) {
    logger.error('Failed to upload contacts:', error);
    throw new DatabaseError(`Failed to upload contacts: ${error}`);
  }
};

export const getUserContacts = async (userId: string): Promise<ContactSyncResponse> => {
  try {
    const userContacts = await UserContacts.findOne({ user_id: userId });

    if (!userContacts) {
      return {
        success: true,
        contacts: [],
        total_contacts: 0,
        registered_contacts: 0
      };
    }

    const contacts: ContactResponse[] = [];
    let registeredCount = 0;

    for (const contactItem of userContacts.contacts) {
      if (contactItem.is_registered) {
        registeredCount++;
      }

      let contactResponse: ContactResponse = {
        contact_id: contactItem.contact_id,
        display_name: contactItem.display_name,
        phone_number: contactItem.phone_number,
        is_registered: contactItem.is_registered,
        registered_user_id: contactItem.registered_user_id,
        added_at: contactItem.added_at,
        updated_at: contactItem.updated_at
      };

      if (contactItem.registered_user_id) {
        try {
          const user = await User.findById(contactItem.registered_user_id);
          if (user) {
            contactResponse.profile_picture = user.profile_picture;
            contactResponse.status = user.status;
            contactResponse.last_seen = user.last_seen;
          }
        } catch (error) {
          logger.warn(`Failed to get user details for contact ${contactItem.contact_id}:`, error);
        }
      }

      contacts.push(contactResponse);
    }

    contacts.sort((a, b) => a.display_name.localeCompare(b.display_name));

    return {
      success: true,
      contacts,
      total_contacts: contacts.length,
      registered_contacts: registeredCount
    };
  } catch (error) {
    logger.error('Failed to get user contacts:', error);
    throw new DatabaseError(`Failed to get user contacts: ${error}`);
  }
};

export const getRegisteredContacts = async (userId: string): Promise<string[]> => {
  try {
    const userContacts = await UserContacts.findOne({ user_id: userId });
    
    if (!userContacts) {
      return [];
    }

    return userContacts.contacts
      .filter(contact => contact.is_registered && contact.registered_user_id)
      .map(contact => contact.registered_user_id!)
      .filter(id => id);
  } catch (error) {
    logger.error('Failed to get registered contacts:', error);
    throw new DatabaseError(`Failed to get registered contacts: ${error}`);
  }
};
