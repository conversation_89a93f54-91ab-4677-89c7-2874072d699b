import mongoose, { Document, Schema } from 'mongoose';

export interface IContactItem {
  contact_id: string;
  display_name: string;
  phone_number: string;
  normalized_phone: string;
  is_registered: boolean;
  registered_user_id?: string;
  added_at: Date;
  updated_at: Date;
}

export interface IUserContacts extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: string;
  contacts: IContactItem[];
  total_contacts: number;
  registered_contacts: number;
  createdAt?: Date;
  updatedAt?: Date;
}

const ContactItemSchema = new Schema<IContactItem>({
  contact_id: { type: String, required: true },
  display_name: { type: String, required: true },
  phone_number: { type: String, required: true },
  normalized_phone: { type: String, required: true },
  is_registered: { type: Boolean, default: false },
  registered_user_id: { type: String },
  added_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

const UserContactsSchema = new Schema<IUserContacts>({
  user_id: { type: String, required: true, unique: true },
  contacts: [ContactItemSchema],
  total_contacts: { type: Number, default: 0 },
  registered_contacts: { type: Number, default: 0 }
}, {
  timestamps: true
});

UserContactsSchema.index({ user_id: 1 });

export const UserContacts = mongoose.model<IUserContacts>('UserContacts', UserContactsSchema);
