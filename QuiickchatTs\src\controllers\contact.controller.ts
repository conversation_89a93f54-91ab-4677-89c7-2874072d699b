import { FastifyRequest, FastifyReply } from 'fastify';
import * as contactService from '@/services/contact.service';
import { ValidationError, NotFoundError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import {
  ApiResponse,
  BulkContactsRequest,
  AuthenticatedRequest
} from '@/types';

export const uploadContacts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { contacts } = request.body as BulkContactsRequest;

  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  if (!contacts || contacts.length === 0) {
    return reply.status(400).send({
      success: false,
      message: 'No contacts provided'
    });
  }

  if (contacts.length > 1000) {
    return reply.status(400).send({
      success: false,
      message: 'Too many contacts. Maximum 1000 contacts allowed per upload'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const response = await contactService.uploadContacts(userId, contacts);

    logger.info(`Contacts uploaded for user ${userId}: ${response.contacts_processed} contacts processed, ${response.contacts_registered} registered`);

    return reply.status(200).send({
      success: true,
      message: response.message,
      data: response
    });
  } catch (error: any) {
    logger.error('Error uploading contacts:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to upload contacts'
    });
  }
};

export const getContacts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const response = await contactService.getUserContacts(userId);

    return reply.status(200).send({
      success: true,
      message: 'Contacts retrieved successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error getting contacts:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve contacts'
    });
  }
};

export const syncContacts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const response = await contactService.getUserContacts(userId);

    logger.info(`Contacts synced for user ${userId}: ${response.total_contacts} total, ${response.registered_contacts} registered`);

    return reply.status(200).send({
      success: true,
      message: 'Contacts synced successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error syncing contacts:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to sync contacts'
    });
  }
};
