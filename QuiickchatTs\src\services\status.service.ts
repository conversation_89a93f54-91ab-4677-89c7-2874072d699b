import { Status, StatusView, IStatus } from '@/models/status.model';
import { User } from '@/models/user.model';
import * as contactService from '@/services/contact.service';
import { DatabaseError, ValidationError, NotFoundError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import {
  CreateStatusRequest,
  StatusResponse,
  StatusSummary
} from '@/types';
import { wsManager } from '@/services/websocket.service';

export const createStatus = async (userId: string, statusData: CreateStatusRequest): Promise<StatusResponse> => {
  try {
    if (statusData.content_type === 'text' && !statusData.caption) {
      throw new ValidationError('Text status requires caption');
    }

    if ((statusData.content_type === 'image' || statusData.content_type === 'video') && !statusData.content_url) {
      throw new ValidationError('Media status requires content URL');
    }

    const status = new Status({
      user_id: userId,
      content_type: statusData.content_type,
      content_url: statusData.content_url,
      caption: statusData.caption,
      background_color: statusData.background_color,
      font_style: statusData.font_style,
      privacy_setting: statusData.privacy_setting || 'all_contacts',
      allowed_contacts: statusData.allowed_contacts || [],
      blocked_contacts: statusData.blocked_contacts || [],
      view_count: 0,
      viewers: [],
      is_active: true,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    });

    await status.save();

    logger.info(`Status created for user ${userId}: ${status._id}`);

    const statusResponse = {
      id: status._id.toString(),
      user_id: status.user_id,
      content_type: status.content_type,
      content_url: status.content_url || undefined,
      caption: status.caption || undefined,
      background_color: status.background_color || undefined,
      font_style: status.font_style || undefined,
      privacy_setting: status.privacy_setting,
      view_count: status.view_count,
      is_active: status.is_active,
      expires_at: status.expires_at,
      created_at: status.createdAt!
    };

    wsManager.broadcastStatusUpdate(userId, statusResponse);

    return statusResponse;
  } catch (error) {
    logger.error('Failed to create status:', error);
    throw new DatabaseError(`Failed to create status: ${error}`);
  }
};

export const getUserStatuses = async (userId: string, viewerId?: string): Promise<StatusResponse[]> => {
  try {
    const statuses = await Status.find({
      user_id: userId,
      is_active: true,
      expires_at: { $gt: new Date() }
    }).sort({ createdAt: -1 });

    const statusResponses: StatusResponse[] = [];

    for (const status of statuses) {
      const statusResponse: StatusResponse = {
        id: status._id.toString(),
        user_id: status.user_id,
        content_type: status.content_type,
        content_url: status.content_url || undefined,
        caption: status.caption || undefined,
        background_color: status.background_color || undefined,
        font_style: status.font_style || undefined,
        privacy_setting: status.privacy_setting,
        view_count: status.view_count,
        is_active: status.is_active,
        expires_at: status.expires_at,
        created_at: status.createdAt!
      };

      if (viewerId && !canViewStatus(status, viewerId)) {
        continue;
      }

      statusResponses.push(statusResponse);
    }

    return statusResponses;
  } catch (error) {
    logger.error('Failed to get user statuses:', error);
    throw new DatabaseError(`Failed to get user statuses: ${error}`);
  }
};

export const getContactStatuses = async (userId: string): Promise<StatusSummary[]> => {
  try {
    const registeredContactIds = await contactService.getRegisteredContacts(userId);
    
    if (registeredContactIds.length === 0) {
      return [];
    }

    const summaries: StatusSummary[] = [];

    for (const contactId of registeredContactIds) {
      const statuses = await Status.find({
        user_id: contactId,
        is_active: true,
        expires_at: { $gt: new Date() }
      }).sort({ createdAt: -1 });

      if (statuses.length === 0) {
        continue;
      }

      const viewableStatuses = statuses.filter(status => canViewStatus(status, userId));
      
      if (viewableStatuses.length === 0) {
        continue;
      }

      const unviewedCount = await getUnviewedStatusCount(contactId, userId);
      const latestStatus = viewableStatuses[0];

      if (!latestStatus || !latestStatus.createdAt) {
        continue;
      }

      const summary: StatusSummary = {
        user_id: contactId,
        total_statuses: viewableStatuses.length,
        unviewed_statuses: unviewedCount,
        latest_status_time: latestStatus.createdAt
      };

      try {
        const user = await User.findById(contactId);
        if (user) {
          summary.profile_picture = user.profile_picture;
        }
      } catch (error) {
        logger.warn(`Failed to get user details for contact ${contactId}:`, error);
      }

      summaries.push(summary);
    }

    summaries.sort((a, b) => b.latest_status_time.getTime() - a.latest_status_time.getTime());

    return summaries;
  } catch (error) {
    logger.error('Failed to get contact statuses:', error);
    throw new DatabaseError(`Failed to get contact statuses: ${error}`);
  }
};

export const viewStatus = async (statusId: string, viewerId: string): Promise<StatusResponse> => {
  try {
    const status = await Status.findById(statusId);
    
    if (!status) {
      throw new NotFoundError('Status not found');
    }

    if (!status.is_active || status.expires_at <= new Date()) {
      throw new NotFoundError('Status is no longer available');
    }

    if (!canViewStatus(status, viewerId)) {
      throw new ValidationError('You do not have permission to view this status');
    }

    const existingView = await StatusView.findOne({
      status_id: statusId,
      viewer_id: viewerId
    });

    if (!existingView) {
      await StatusView.create({
        status_id: statusId,
        viewer_id: viewerId,
        viewed_at: new Date()
      });

      if (!status.viewers.includes(viewerId)) {
        status.viewers.push(viewerId);
        status.view_count = status.viewers.length;
        await status.save();

        wsManager.broadcastStatusView(statusId, viewerId, status.user_id);
      }
    }

    return {
      id: status._id.toString(),
      user_id: status.user_id,
      content_type: status.content_type,
      content_url: status.content_url || undefined,
      caption: status.caption || undefined,
      background_color: status.background_color || undefined,
      font_style: status.font_style || undefined,
      privacy_setting: status.privacy_setting,
      view_count: status.view_count,
      is_active: status.is_active,
      expires_at: status.expires_at,
      created_at: status.createdAt!
    };
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof ValidationError) {
      throw error;
    }
    logger.error('Failed to view status:', error);
    throw new DatabaseError(`Failed to view status: ${error}`);
  }
};

const canViewStatus = (status: IStatus, viewerId: string): boolean => {
  if (status.user_id === viewerId) {
    return true;
  }

  switch (status.privacy_setting) {
    case 'all_contacts':
      return !status.blocked_contacts.includes(viewerId);
    case 'selected_contacts':
      return status.allowed_contacts.includes(viewerId);
    case 'except_contacts':
      return !status.blocked_contacts.includes(viewerId);
    default:
      return false;
  }
};

const getUnviewedStatusCount = async (statusOwnerId: string, viewerId: string): Promise<number> => {
  try {
    const statuses = await Status.find({
      user_id: statusOwnerId,
      is_active: true,
      expires_at: { $gt: new Date() }
    });

    const viewableStatuses = statuses.filter(status => canViewStatus(status, viewerId));
    
    let unviewedCount = 0;
    for (const status of viewableStatuses) {
      const hasViewed = await StatusView.exists({
        status_id: status._id.toString(),
        viewer_id: viewerId
      });
      
      if (!hasViewed) {
        unviewedCount++;
      }
    }

    return unviewedCount;
  } catch (error) {
    logger.error('Failed to get unviewed status count:', error);
    return 0;
  }
};
