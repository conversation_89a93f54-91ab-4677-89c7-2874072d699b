import { FastifyRequest, FastifyReply } from 'fastify';
import * as statusService from '@/services/status.service';
import { uploadSingleFile } from '@/services/cloudinary.service';
import { ValidationError, NotFoundError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import {
  ApiResponse,
  CreateStatusRequest,
  ViewStatusRequest,
  AuthenticatedRequest
} from '@/types';

export const createStatus = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const statusData = request.body as CreateStatusRequest;

  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  if (!statusData.content_type) {
    return reply.status(400).send({
      success: false,
      message: 'Content type is required'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const status = await statusService.createStatus(userId, statusData);

    logger.info(`Status created for user ${userId}: ${status.id}`);

    return reply.status(201).send({
      success: true,
      message: 'Status created successfully',
      data: status
    });
  } catch (error: any) {
    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    logger.error('Error creating status:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to create status'
    });
  }
};

export const uploadStatusMedia = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const mediaUrl = await uploadSingleFile(request, 'statusMedia');
    
    if (!mediaUrl) {
      return reply.status(400).send({
        success: false,
        message: 'No file uploaded'
      });
    }

    const userId = request.user.userId || request.user.phone;
    
    if (!request.body) {
      return reply.status(400).send({
        success: false,
        message: 'Request body is required'
      });
    }

    const body = request.body as any;
    const statusData: CreateStatusRequest = {
      content_type: mediaUrl.includes('.mp4') || mediaUrl.includes('.mov') ? 'video' : 'image',
      content_url: mediaUrl,
      caption: body?.caption || '',
      privacy_setting: body?.privacy_setting || 'all_contacts',
      allowed_contacts: body?.allowed_contacts || [],
      blocked_contacts: body?.blocked_contacts || []
    };

    const status = await statusService.createStatus(userId, statusData);

    logger.info(`Status with media created for user ${userId}: ${status.id}`);

    return reply.status(201).send({
      success: true,
      message: 'Status with media created successfully',
      data: status
    });
  } catch (error: any) {
    logger.error('Error uploading status media:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to upload status media'
    });
  }
};

export const getMyStatuses = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const statuses = await statusService.getUserStatuses(userId);

    return reply.status(200).send({
      success: true,
      message: 'Statuses retrieved successfully',
      data: statuses
    });
  } catch (error: any) {
    logger.error('Error getting my statuses:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve statuses'
    });
  }
};

export const getContactStatuses = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const summaries = await statusService.getContactStatuses(userId);

    return reply.status(200).send({
      success: true,
      message: 'Contact statuses retrieved successfully',
      data: summaries
    });
  } catch (error: any) {
    logger.error('Error getting contact statuses:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve contact statuses'
    });
  }
};

export const viewStatus = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { status_id } = request.body as ViewStatusRequest;

  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  if (!status_id) {
    return reply.status(400).send({
      success: false,
      message: 'Status ID is required'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const status = await statusService.viewStatus(status_id, userId);

    return reply.status(200).send({
      success: true,
      message: 'Status viewed successfully',
      data: status
    });
  } catch (error: any) {
    if (error instanceof NotFoundError) {
      return reply.status(404).send({
        success: false,
        message: error.message
      });
    }

    if (error instanceof ValidationError) {
      return reply.status(403).send({
        success: false,
        message: error.message
      });
    }

    logger.error('Error viewing status:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to view status'
    });
  }
};
