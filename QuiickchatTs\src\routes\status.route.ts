import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  createStatus,
  uploadStatusMedia,
  getMyStatuses,
  getContactStatuses,
  viewStatus
} from '@/controllers/status.controller';
import { CreateStatusRequest, ViewStatusRequest } from '@/types';
import { authenticateRequest } from '@/middleware/auth.middleware';

export async function statusRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.post('/', { preHandler: authenticateRequest }, createStatus as any);
  fastify.post('/upload', { preHandler: authenticateRequest }, uploadStatusMedia as any);
  fastify.get('/me', { preHandler: authenticateRequest }, getMyStatuses as any);
  fastify.get('/contacts', { preHandler: authenticateRequest }, getContactStatuses as any);
  fastify.post('/view', { preHandler: authenticateRequest }, viewStatus as any);
}
