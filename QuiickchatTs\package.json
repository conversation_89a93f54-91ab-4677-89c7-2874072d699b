{"name": "quickchat-backend-ts", "version": "1.0.0", "description": "QuickChat backend application built with Fastify and TypeScript", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec \"ts-node -r tsconfig-paths/register\" src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "start:dev": "node -r tsconfig-paths/register dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:zengo": "node run_zengo_tests.js", "test:zengo-api": "node run_zengo_tests.js --api-only", "test:zengo-local": "node run_zengo_tests.js --local-only", "zengo:enable": "node enable_zengo.js", "zengo:disable": "node disable_zengo.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["quickchat", "messaging", "fastify", "typescript", "mongodb"], "author": "Olajosh80", "license": "MIT", "dependencies": {"@fastify/cors": "^8.4.0", "@fastify/jwt": "^7.2.4", "@fastify/multipart": "^8.0.0", "@fastify/websocket": "^10.0.1", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "cloudinary-build-url": "^0.2.4", "dotenv": "^16.3.1", "fastify": "^4.24.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "tsconfig-paths": "^4.2.0", "twilio": "^4.19.0", "uuid": "^9.0.1", "validator": "^13.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@types/validator": "^13.11.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsc-alias": "^1.8.16", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}