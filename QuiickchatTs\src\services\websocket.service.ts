import { FastifyInstance } from 'fastify';
import { Auth } from '@/middleware/auth.middleware';
import { logger } from '@/utils/logger';
import { AuthenticatedRequest } from '@/types';

interface ConnectedClient {
  socket: any;
  phone: string;
  subscriptions: Set<string>;
}

class WebSocketManager {
  private clients: Map<string, ConnectedClient> = new Map();
  private phoneToClientId: Map<string, string> = new Map();

  addClient(clientId: string, socket: any, phone: string): void {
    const client: ConnectedClient = {
      socket,
      phone,
      subscriptions: new Set()
    };

    this.clients.set(clientId, client);
    this.phoneToClientId.set(phone, clientId);
    
    logger.info(`Client connected: ${phone} (${clientId})`);
  }

  removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      this.phoneToClientId.delete(client.phone);
      this.clients.delete(clientId);
      logger.info(`Client disconnected: ${client.phone} (${clientId})`);
    }
  }

  subscribeToContact(clientId: string, contactPhone: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.add(contactPhone);
      logger.info(`${client.phone} subscribed to ${contactPhone}`);
    }
  }

  unsubscribeFromContact(clientId: string, contactPhone: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.delete(contactPhone);
      logger.info(`${client.phone} unsubscribed from ${contactPhone}`);
    }
  }

  broadcastStatusUpdate(userPhone: string, statusData: any): void {
    const subscriberClients = Array.from(this.clients.values()).filter(
      client => client.subscriptions.has(userPhone)
    );

    const message = JSON.stringify({
      type: 'status_update',
      user_phone: userPhone,
      data: statusData,
      timestamp: new Date().toISOString()
    });

    subscriberClients.forEach(client => {
      try {
        client.socket.send(message);
        logger.info(`Status update sent to ${client.phone} for ${userPhone}`);
      } catch (error) {
        logger.error(`Failed to send status update to ${client.phone}:`, error);
      }
    });
  }

  broadcastStatusView(statusId: string, viewerPhone: string, statusOwnerPhone: string): void {
    const ownerClientId = this.phoneToClientId.get(statusOwnerPhone);
    if (ownerClientId) {
      const ownerClient = this.clients.get(ownerClientId);
      if (ownerClient) {
        const message = JSON.stringify({
          type: 'status_viewed',
          status_id: statusId,
          viewer_phone: viewerPhone,
          timestamp: new Date().toISOString()
        });

        try {
          ownerClient.socket.send(message);
          logger.info(`Status view notification sent to ${statusOwnerPhone}`);
        } catch (error) {
          logger.error(`Failed to send view notification to ${statusOwnerPhone}:`, error);
        }
      }
    }
  }

  getClientCount(): number {
    return this.clients.size;
  }

  getSubscriptionCount(userPhone: string): number {
    return Array.from(this.clients.values()).filter(
      client => client.subscriptions.has(userPhone)
    ).length;
  }
}

export const wsManager = new WebSocketManager();

export const setupWebSocket = async (fastify: FastifyInstance): Promise<void> => {
  await fastify.register(require('@fastify/websocket'));

  fastify.register(async function (fastify) {
    (fastify as any).get('/status-updates', { websocket: true }, (connection: any, req: any) => {
      const clientId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      connection.socket.on('message', async (message: any) => {
        try {
          const data = JSON.parse(message.toString());
          
          if (data.type === 'auth') {
            const token = data.token;
            if (!token) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Authentication token required'
              }));
              connection.socket.close();
              return;
            }

            try {
              const claims = Auth.validateAccessToken(token);
              wsManager.addClient(clientId, connection.socket, claims.phone);
              
              connection.socket.send(JSON.stringify({
                type: 'auth_success',
                message: 'Connected successfully'
              }));
            } catch (error) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Invalid authentication token'
              }));
              connection.socket.close();
              return;
            }
          }

          if (data.type === 'subscribe') {
            const contactPhone = data.contact_phone;
            if (contactPhone) {
              wsManager.subscribeToContact(clientId, contactPhone);
              connection.socket.send(JSON.stringify({
                type: 'subscribed',
                contact_phone: contactPhone
              }));
            }
          }

          if (data.type === 'unsubscribe') {
            const contactPhone = data.contact_phone;
            if (contactPhone) {
              wsManager.unsubscribeFromContact(clientId, contactPhone);
              connection.socket.send(JSON.stringify({
                type: 'unsubscribed',
                contact_phone: contactPhone
              }));
            }
          }

          if (data.type === 'ping') {
            connection.socket.send(JSON.stringify({
              type: 'pong',
              timestamp: new Date().toISOString()
            }));
          }

        } catch (error) {
          logger.error('WebSocket message error:', error);
          connection.socket.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      connection.socket.on('close', () => {
        wsManager.removeClient(clientId);
      });

      connection.socket.on('error', (error: any) => {
        logger.error('WebSocket error:', error);
        wsManager.removeClient(clientId);
      });
    });
  });

  logger.info('WebSocket service initialized');
};
