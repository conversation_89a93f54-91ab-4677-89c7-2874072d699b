import { FastifyInstance, FastifyRequest, FastifyReply, FastifyError } from 'fastify';
import { AppError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import { ApiResponse } from '@/types';

export async function errorHandler(fastify: FastifyInstance) {
  fastify.setErrorHandler(async (error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
    logger.error('Error occurred:', {
      error: error.message,
      stack: error.stack,
      url: request.url,
      method: request.method,
      ip: request.ip
    });

    if (error instanceof AppError) {
      const response: ApiResponse = {
        success: false,
        message: error.message
      };

      return reply.status(error.statusCode).send(response);
    }

    if (error.validation) {
      const response: ApiResponse = {
        success: false,
        message: 'Validation error',
        data: error.validation
      };

      return reply.status(400).send(response);
    }

    if (error.message.includes('jwt') || error.message.includes('token')) {
      const response: ApiResponse = {
        success: false,
        message: 'Authentication failed'
      };

      return reply.status(401).send(response);
    }

    if (error.name === 'MongoError' || error.name === 'ValidationError') {
      const response: ApiResponse = {
        success: false,
        message: 'Database error occurred'
      };

      return reply.status(500).send(response);
    }

    const response: ApiResponse = {
      success: false,
      message: 'Internal server error'
    };

    return reply.status(500).send(response);
  });
}
