import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  getCurrentUser,
  updateProfile,
  deleteAccount,
  uploadProfilePicture
} from '@/controllers/user.controller';
import { initUser } from '@/controllers/auth.controller';
import { UpdateProfileRequest, UserQuery } from '@/types';
import { authenticateRequest } from '@/middleware/auth.middleware';

export async function userRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/init', initUser as any);
  fastify.get('/me', { preHandler: authenticateRequest }, getCurrentUser as any);
  fastify.put('/update-profile', { preHandler: authenticateRequest }, updateProfile as any);
  fastify.post('/upload-profile-picture', { preHandler: authenticateRequest }, uploadProfilePicture as any);
  fastify.delete('/delete-account', { preHandler: authenticateRequest }, deleteAccount as any);
}
