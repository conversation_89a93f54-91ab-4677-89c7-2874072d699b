import crypto from 'crypto';
import axios, { AxiosResponse } from 'axios';
import { config } from '@/config';
import { ExternalServiceError, ConfigError, ValidationError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import {
  ZengoUserRegisterRequest,
  ZengoUserRegisterResponse,
  ZengoUserInfoRequest,
  ZengoUserInfoResponse,
  ZengoOnlineStatusResponse,
  ZengoUserInfo,
  ZengoApiRequest,
  ZengoModifyUserRequest,
  ZengoModifyUserInfo,
  ZengoAddFriendsRequest,
  ZengoFriendInfo,
  ZengoSendFriendRequestRequest,
  ZengoFriendRequestInfo,
  ZengoDeleteFriendsRequest,
  ZengoDeleteAllFriendsRequest,
  ZengoQueryFriendListRequest,
  ZengoQueryFriendListResponse,
  ZengoUpdateFriendsAliasRequest,
  ZengoFriendAliasUpdate,
  ZengoUpdateFriendAttributesRequest,
  ZengoBlockUsersRequest,
  ZengoUnblockUsersRequest,
  ZengoFriendAttribute,
  ZengoQueryBlocklistRequest,
  ZengoQueryBlocklistResponse,
  ZengoCheckBlockshipRequest,
  ZengoCheckBlockshipResponse
} from '@/types';

const zengoConfig = config.zengo;

const getApiBaseUrl = (region: string): string => {
  const regionMap = {
    'sha': 'zim-api-sha.zego.im',
    'hkg': 'zim-api-hkg.zego.im', 
    'fra': 'zim-api-fra.zego.im',
    'lax': 'zim-api-lax.zego.im',
    'bom': 'zim-api-bom.zego.im',
    'sgp': 'zim-api-sgp.zego.im',
    'unified': 'zim-api.zego.im'
  };
  
  return `https://${regionMap[region as keyof typeof regionMap] || regionMap.unified}`;
};

const generateSignature = (appId: string, signatureNonce: string, serverSecret: string, timestamp: number): string => {
  const hash = crypto.createHash('md5');
  const str = appId + signatureNonce + serverSecret + timestamp;
  hash.update(str);
  return hash.digest('hex');
};

const generateSignatureNonce = (): string => {
  return crypto.randomBytes(8).toString('hex');
};

const isServiceEnabled = (): boolean => {
  return zengoConfig.enabled && !!zengoConfig.appId && !!zengoConfig.serverSecret;
};

const buildApiRequest = (action: string, additionalParams: Record<string, any> = {}): ZengoApiRequest & Record<string, any> => {
  if (!isServiceEnabled()) {
    throw new ConfigError('Zengo service not properly configured');
  }

  const timestamp = Math.round(Date.now() / 1000);
  const signatureNonce = generateSignatureNonce();
  const signature = generateSignature(zengoConfig.appId, signatureNonce, zengoConfig.serverSecret, timestamp);

  return {
    Action: action,
    AppId: zengoConfig.appId,
    SignatureNonce: signatureNonce,
    Timestamp: timestamp,
    Signature: signature,
    SignatureVersion: '2.0',
    ...additionalParams
  };
};

const makeApiCall = async <T>(
  action: string, 
  method: 'GET' | 'POST' = 'POST',
  data?: any,
  queryParams?: Record<string, any>
): Promise<T> => {
  if (!isServiceEnabled()) {
    logger.info(`Zengo service is disabled. Would have called ${action}`);
    throw new ConfigError('Zeng service not enabled');
  }

  try {
    const baseUrl = getApiBaseUrl(zengoConfig.region);
    const apiRequest = buildApiRequest(action, queryParams);
    
    let response: AxiosResponse<T>;
    
    if (method === 'GET') {
      const params = new URLSearchParams();
      Object.entries(apiRequest).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(item => params.append(`${key}[]`, item));
        } else {
          params.append(key, value.toString());
        }
      });
      
      response = await axios.get(`${baseUrl}/?${params.toString()}`);
    } else {
      const params = new URLSearchParams();
      Object.entries(apiRequest).forEach(([key, value]) => {
        if (!['Action', 'AppId', 'SignatureNonce', 'Timestamp', 'Signature', 'SignatureVersion'].includes(key)) {
          return;
        }
        params.append(key, value.toString());
      });
      
      response = await axios.post(`${baseUrl}/?${params.toString()}`, data, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    logger.info(`Zengo API call successful: ${action}`);
    return response.data;
  } catch (error: any) {
    logger.error(`Zengo API call failed for ${action}:`, error);
    
    if (error.response?.data) {
      const errorData = error.response.data;
      throw new ExternalServiceError(`Zengo API error: ${errorData.Message || error.message}`);
    }
    
    throw new ExternalServiceError(`Failed to call Zengo API: ${error.message}`);
  }
};

export const registerUser = async (userId: string, userName?: string, userAvatar?: string): Promise<ZengoUserRegisterResponse> => {
  if (!userId) {
    throw new ValidationError('User ID is required for Zengo registration');
  }

  const userInfo: ZengoUserInfo = {
    UserId: userId,
    ...(userName && { UserName: userName }),
    ...(userAvatar && { UserAvatar: userAvatar })
  };

  const requestData: ZengoUserRegisterRequest = {
    UserInfo: [userInfo]
  };

  try {
    logger.info(`Registering user with Zengo: ${userId}`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('UserRegister', 'POST', requestData);
    
    if (response.Code === 0) {
      logger.info(`User successfully registered with Zengo: ${userId}`);
    } else {
      logger.warn(`Zengo registration returned non-zero code for ${userId}: ${response.Code} - ${response.Message}`);
    }
    
    return response;
  } catch (error) {
    logger.error(`Failed to register user with Zengo: ${userId}`, error);
    throw error;
  }
};

export const batchRegisterUsers = async (users: ZengoUserInfo[]): Promise<ZengoUserRegisterResponse> => {
  if (!users || users.length === 0) {
    throw new ValidationError('At least one user is required for batch registration');
  }

  if (users.length > 100) {
    throw new ValidationError('Maximum 100 users can be registered in a single batch');
  }

  const requestData: ZengoUserRegisterRequest = {
    UserInfo: users
  };

  try {
    logger.info(`Batch registering ${users.length} users with Zengo`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('UserRegister', 'POST', requestData);
    
    logger.info(`Batch registration completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to batch register users with Zengo', error);
    throw error;
  }
};

export const getUserInfo = async (userIds: string[]): Promise<ZengoUserInfoResponse> => {
  if (!userIds || userIds.length === 0) {
    throw new ValidationError('At least one user ID is required');
  }

  if (userIds.length > 100) {
    throw new ValidationError('Maximum 100 user IDs can be queried at once');
  }

  const requestData: ZengoUserInfoRequest = {
    UserIds: userIds
  };

  try {
    logger.info(`Querying user info for ${userIds.length} users from Zengo`);
    const response = await makeApiCall<ZengoUserInfoResponse>('QueryUserInfos', 'POST', requestData);
    
    return response;
  } catch (error) {
    logger.error('Failed to get user info from Zengo', error);
    throw error;
  }
};

export const getUserOnlineStatus = async (userIds: string[]): Promise<ZengoOnlineStatusResponse> => {
  if (!userIds || userIds.length === 0) {
    throw new ValidationError('At least one user ID is required');
  }

  if (userIds.length > 100) {
    throw new ValidationError('Maximum 100 user IDs can be queried at once');
  }

  try {
    logger.info(`Querying online status for ${userIds.length} users from Zengo`);
    const queryParams = {
      UserId: userIds
    };
    
    const response = await makeApiCall<ZengoOnlineStatusResponse>('QueryUserOnlineState', 'GET', undefined, queryParams);
    
    return response;
  } catch (error) {
    logger.error('Failed to get user online status from Zengo', error);
    throw error;
  }
};

export const getServiceStatus = (): { enabled: boolean; configured: boolean } => {
  return {
    enabled: zengoConfig.enabled,
    configured: !!(zengoConfig.appId && zengoConfig.serverSecret)
  };
};

export const modifyUserInfo = async (users: ZengoModifyUserInfo[]): Promise<ZengoUserRegisterResponse> => {
  if (!users || users.length === 0) {
    throw new ValidationError('At least one user is required for modification');
  }

  if (users.length > 100) {
    throw new ValidationError('Maximum 100 users can be modified in a single batch');
  }

  const requestData: ZengoModifyUserRequest = {
    UserInfo: users
  };

  try {
    logger.info(`Modifying info for ${users.length} users in Mother chat`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('ModifyUserInfo', 'POST', requestData);

    logger.info(`User info modification completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to modify user info in Mother chat', error);
    throw error;
  }
};

export const addFriends = async (fromUserId: string, friends: ZengoFriendInfo[]): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (!friends || friends.length === 0) {
    throw new ValidationError('At least one friend is required');
  }

  if (friends.length > 20) {
    throw new ValidationError('Maximum 20 friends can be added in a single batch');
  }

  const requestData: ZengoAddFriendsRequest = {
    FromUserId: fromUserId,
    FriendInfos: friends
  };

  try {
    logger.info(`Adding ${friends.length} friends for user ${fromUserId} in Mother chat`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('AddFriends', 'POST', requestData);

    logger.info(`Friend addition completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to add friends in Mother chat', error);
    throw error;
  }
};

export const sendFriendRequests = async (fromUserId: string, requests: ZengoFriendRequestInfo[]): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (!requests || requests.length === 0) {
    throw new ValidationError('At least one friend request is required');
  }

  if (requests.length > 20) {
    throw new ValidationError('Maximum 20 friend requests can be sent in a single batch');
  }

  const requestData: ZengoSendFriendRequestRequest = {
    FromUserId: fromUserId,
    FriendInfos: requests
  };

  try {
    logger.info(`Sending ${requests.length} friend requests for user ${fromUserId} in Mother chat`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('SendFriendApplication', 'POST', requestData);

    logger.info(`Friend requests sent. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to send friend requests in Mother chat', error);
    throw error;
  }
};

export const deleteFriends = async (fromUserId: string, userIds: string[], deleteType: 0 | 1): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (!userIds || userIds.length === 0) {
    throw new ValidationError('At least one user ID is required for deletion');
  }

  if (userIds.length > 20) {
    throw new ValidationError('Maximum 20 friends can be deleted in a single batch');
  }

  const requestData: ZengoDeleteFriendsRequest = {
    FromUserId: fromUserId,
    UserIds: userIds,
    DeleteType: deleteType
  };

  try {
    logger.info(`Deleting ${userIds.length} friends for user ${fromUserId} in Mother chat (${deleteType === 0 ? 'two-way' : 'one-way'})`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('DeleteFriends', 'POST', requestData);

    logger.info(`Friend deletion completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to delete friends in Mother chat', error);
    throw error;
  }
};

export const deleteAllFriends = async (fromUserId: string, deleteType: 0 | 1): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  try {
    logger.info(`Deleting all friends for user ${fromUserId} in Mother chat (${deleteType === 0 ? 'two-way' : 'one-way'})`);

    const queryParams = {
      FromUserId: fromUserId,
      DeleteType: deleteType
    };

    const response = await makeApiCall<ZengoUserRegisterResponse>('DeleteAllFriends', 'GET', undefined, queryParams);

    logger.info(`All friends deletion completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to delete all friends in Mother chat', error);
    throw error;
  }
};

export const queryFriendList = async (fromUserId: string, limit: number = 100, next: number = 0): Promise<ZengoQueryFriendListResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (limit < 0 || limit > 100) {
    limit = 100;
  }

  try {
    logger.info(`Querying friend list for user ${fromUserId} in Mother chat (limit: ${limit}, next: ${next})`);

    const queryParams = {
      FromUserId: fromUserId,
      Limit: limit,
      Next: next
    };

    const response = await makeApiCall<ZengoQueryFriendListResponse>('QueryFriendList', 'GET', undefined, queryParams);

    logger.info(`Friend list query completed. Code: ${response.Code}, Total: ${response.TotalCount || 0}`);
    return response;
  } catch (error) {
    logger.error('Failed to query friend list in Mother chat', error);
    throw error;
  }
};

export const updateFriendsAlias = async (fromUserId: string, aliasUpdates: ZengoFriendAliasUpdate[]): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (!aliasUpdates || aliasUpdates.length === 0) {
    throw new ValidationError('At least one alias update is required');
  }

  if (aliasUpdates.length > 20) {
    throw new ValidationError('Maximum 20 friend aliases can be updated in a single batch');
  }

  const requestData: ZengoUpdateFriendsAliasRequest = {
    FromUserId: fromUserId,
    UserIds: aliasUpdates
  };

  try {
    logger.info(`Updating aliases for ${aliasUpdates.length} friends for user ${fromUserId} in Mother chat`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('UpdateFriendsAlias', 'POST', requestData);

    logger.info(`Friend alias update completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to update friend aliases in Mother chat', error);
    throw error;
  }
};

export const updateFriendAttributes = async (fromUserId: string, userId: string, attributes: ZengoFriendAttribute[], action: number = 0): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId || !userId) {
    throw new ValidationError('From user ID and user ID are required');
  }

  if (!attributes || attributes.length === 0) {
    throw new ValidationError('At least one attribute is required');
  }

  if (attributes.length > 5) {
    throw new ValidationError('Maximum 5 attributes can be updated at once');
  }

  const requestData: ZengoUpdateFriendAttributesRequest = {
    FromUserId: fromUserId,
    UserId: userId,
    Attributes: attributes,
    Action: action
  };

  try {
    logger.info(`Updating ${attributes.length} attributes for friend ${userId} of user ${fromUserId} in Mother chat`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('UpdateFriendAttributes', 'POST', requestData);

    logger.info(`Friend attributes update completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to update friend attributes in Mother chat', error);
    throw error;
  }
};

export const blockUsers = async (fromUserId: string, userIds: string[]): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (!userIds || userIds.length === 0) {
    throw new ValidationError('At least one user ID is required for blocking');
  }

  if (userIds.length > 20) {
    throw new ValidationError('Maximum 20 users can be blocked in a single batch');
  }

  const requestData: ZengoBlockUsersRequest = {
    FromUserId: fromUserId,
    UserIds: userIds
  };

  try {
    logger.info(`Blocking ${userIds.length} users for user ${fromUserId} in Mother chat`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('AddUsersToBlacklist', 'POST', requestData);

    logger.info(`User blocking completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to block users in Mother chat', error);
    throw error;
  }
};

export const unblockUsers = async (fromUserId: string, userIds: string[]): Promise<ZengoUserRegisterResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (!userIds || userIds.length === 0) {
    throw new ValidationError('At least one user ID is required for unblocking');
  }

  if (userIds.length > 20) {
    throw new ValidationError('Maximum 20 users can be unblocked in a single batch');
  }

  const requestData: ZengoUnblockUsersRequest = {
    FromUserId: fromUserId,
    UserIds: userIds
  };

  try {
    logger.info(`Unblocking ${userIds.length} users for user ${fromUserId} in Mother chat`);
    const response = await makeApiCall<ZengoUserRegisterResponse>('RemoveUsersFromBlacklist', 'POST', requestData);

    logger.info(`User unblocking completed. Code: ${response.Code}, Message: ${response.Message}`);
    return response;
  } catch (error) {
    logger.error('Failed to unblock users in Mother chat', error);
    throw error;
  }
};

export const queryBlocklist = async (fromUserId: string, limit: number = 100, next: number = 0): Promise<ZengoQueryBlocklistResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (limit < 0 || limit > 100) {
    limit = 100;
  }

  try {
    logger.info(`Querying blocklist for user ${fromUserId} in Mother chat (limit: ${limit}, next: ${next})`);

    const queryParams = {
      FromUserId: fromUserId,
      Limit: limit,
      Next: next
    };

    const response = await makeApiCall<ZengoQueryBlocklistResponse>('QueryBlacklist', 'GET', undefined, queryParams);

    logger.info(`Blocklist query completed. Code: ${response.Code}, Total: ${response.TotalCount || 0}`);
    return response;
  } catch (error) {
    logger.error('Failed to query blocklist in Mother chat', error);
    throw error;
  }
};

export const checkBlockship = async (fromUserId: string, userIds: string[]): Promise<ZengoCheckBlockshipResponse> => {
  if (!fromUserId) {
    throw new ValidationError('From user ID is required');
  }

  if (!userIds || userIds.length === 0) {
    throw new ValidationError('At least one user ID is required for blockship check');
  }

  if (userIds.length > 20) {
    throw new ValidationError('Maximum 20 users can be checked for blockship in a single batch');
  }

  const requestData: ZengoCheckBlockshipRequest = {
    FromUserId: fromUserId,
    UserIds: userIds
  };

  try {
    logger.info(`Checking blockship for ${userIds.length} users against user ${fromUserId} in Mother chat`);
    const response = await makeApiCall<ZengoCheckBlockshipResponse>('CheckUsersIsInBlacklist', 'POST', requestData);

    logger.info(`Blockship check completed. Code: ${response.Code}, Successful checks: ${response.Succ?.length || 0}`);
    return response;
  } catch (error) {
    logger.error('Failed to check blockship in Mother chat', error);
    throw error;
  }
};

export const isZengoEnabled = (): boolean => {
  return isServiceEnabled();
};
