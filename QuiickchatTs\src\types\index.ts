import { FastifyRequest } from 'fastify';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface UpdateProfileRequest {
  username?: string;
  bio?: string;
  address?: string;
}

export interface UserQuery {
  phone?: string;
  user_id?: string;
}

export type AuthenticatedRequest = FastifyRequest & {
  user?: {
    phone: string;
    userId?: string;
    roles?: string[];
  };
};

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface RegisterRequest {
  phone: string;
}

export interface VerifyRequest {
  phone: string;
  code: string;
}

export interface LoginRequest {
  phone: string;
}

export interface VerifyLoginRequest {
  phone: string;
  code: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface JWTClaims {
  phone: string;
  exp: number;
  iat: number;
  jti: string;
  token_type: 'access' | 'refresh';
  roles?: string[];
}

export interface DatabaseConfig {
  uri: string;
  dbName: string;
}

export interface TwilioConfig {
  accountSid: string;
  authToken: string;
  fromNumber: string;
  enabled: boolean;
}

export interface CloudinaryConfig {
  cloudName: string;
  apiKey: string;
  apiSecret: string;
}

export interface ZengoConfig {
  appId: string;
  serverSecret: string;
  region: 'sha' | 'hkg' | 'fra' | 'lax' | 'bom' | 'sgp' | 'unified';
  enabled: boolean;
}

export interface ContactInput {
  display_name: string;
  phone_number: string;
}

export interface BulkContactsRequest {
  contacts: ContactInput[];
}

export interface ContactResponse {
  contact_id: string;
  display_name: string;
  phone_number: string;
  is_registered: boolean;
  registered_user_id?: string | undefined;
  profile_picture?: string | undefined;
  status?: string | undefined;
  last_seen?: Date | undefined;
  added_at: Date;
  updated_at: Date;
}

export interface BulkContactsResponse {
  success: boolean;
  message: string;
  contacts_processed: number;
  contacts_registered: number;
  contacts: ContactResponse[];
}

export interface ContactSyncResponse {
  success: boolean;
  contacts: ContactResponse[];
  total_contacts: number;
  registered_contacts: number;
}

export interface CreateStatusRequest {
  content_type: 'text' | 'image' | 'video';
  content_url?: string;
  caption?: string;
  background_color?: string;
  font_style?: string;
  privacy_setting?: 'all_contacts' | 'selected_contacts' | 'except_contacts';
  allowed_contacts?: string[];
  blocked_contacts?: string[];
}

export interface StatusResponse {
  id: string;
  user_id: string;
  content_type: 'text' | 'image' | 'video';
  content_url?: string | undefined;
  caption?: string | undefined;
  background_color?: string | undefined;
  font_style?: string | undefined;
  privacy_setting: 'all_contacts' | 'selected_contacts' | 'except_contacts';
  view_count: number;
  is_active: boolean;
  expires_at: Date;
  created_at: Date;
  display_name?: string | undefined;
  phone_number?: string | undefined;
  profile_picture?: string | undefined;
}

export interface StatusSummary {
  user_id: string;
  display_name?: string | undefined;
  phone_number?: string | undefined;
  profile_picture?: string | undefined;
  total_statuses: number;
  unviewed_statuses: number;
  latest_status_time: Date;
}

export interface ZengoUserInfo {
  UserId: string;
  UserName?: string;
  UserAvatar?: string;
}

export interface ZengoUserRegisterRequest {
  UserInfo: ZengoUserInfo[];
}

export interface ZengoUserRegisterResponse {
  Code: number;
  Message: string;
  RequestId: string;
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoErrorItem {
  UserId: string;
  SubCode: number;
  SubMessage?: string;
}

export interface ZengoUserInfoRequest {
  UserIds: string[];
}

export interface ZengoUserInfoResponse {
  Code: number;
  Message: string;
  RequestId: string;
  Result?: ZengoUserInfoResult[];
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoUserInfoResult {
  UserId: string;
  UserName: string;
  UserAvatar: string;
  Extra: string;
}

export interface ZengoOnlineStatusResponse {
  Code: number;
  Message: string;
  RequestId: string;
  Result?: ZengoOnlineStatusResult[];
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoOnlineStatusResult {
  UserId: string;
  Status: 'Online' | 'Offline';
}

export interface ZengoApiRequest {
  Action: string;
  AppId: string;
  SignatureNonce: string;
  Timestamp: number;
  Signature: string;
  SignatureVersion: string;
}

export interface ZengoModifyUserRequest {
  UserInfo: ZengoModifyUserInfo[];
}

export interface ZengoModifyUserInfo {
  UserId: string;
  UserName?: string;
  UserAvatar?: string;
  Extra?: string;
}

export interface ZengoAddFriendsRequest {
  FromUserId: string;
  FriendInfos: ZengoFriendInfo[];
}

export interface ZengoFriendInfo {
  UserId: string;
  Wording?: string;
  FriendAlias?: string;
  FriendTime?: number;
  Attributes?: ZengoFriendAttribute[];
}

export interface ZengoFriendAttribute {
  Key: 'k0' | 'k1' | 'k2' | 'k3' | 'k4';
  Value: string;
}

export interface ZengoSendFriendRequestRequest {
  FromUserId: string;
  FriendInfos: ZengoFriendRequestInfo[];
}

export interface ZengoFriendRequestInfo {
  UserId: string;
  Wording?: string;
  FriendAlias?: string;
  CreateTime?: number;
  UpdateTime?: number;
  Attributes?: ZengoFriendAttribute[];
}

export interface ZengoDeleteFriendsRequest {
  FromUserId: string;
  UserIds: string[];
  DeleteType: 0 | 1; // 0: two-way, 1: one-way
}

export interface ZengoDeleteAllFriendsRequest {
  FromUserId: string;
  DeleteType: 0 | 1; // 0: two-way, 1: one-way
}

export interface ZengoQueryFriendListRequest {
  FromUserId: string;
  Limit: number;
  Next: number;
}

export interface ZengoQueryFriendListResponse {
  Code: number;
  Message: string;
  RequestId: string;
  TotalCount?: number;
  Next?: number;
  FriendInfos?: ZengoFriendListItem[];
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoFriendListItem {
  UserId: string;
  UserName: string;
  Avatar: string;
  Wording: string;
  FriendAlias: string;
  CreateTime: number;
  Attributes: ZengoFriendAttribute[];
}

export interface ZengoUpdateFriendsAliasRequest {
  FromUserId: string;
  UserIds: ZengoFriendAliasUpdate[];
}

export interface ZengoFriendAliasUpdate {
  UserId: string;
  FriendAlias?: string;
}

export interface ZengoUpdateFriendAttributesRequest {
  FromUserId: string;
  UserId: string;
  Attributes: ZengoFriendAttribute[];
  Action?: number; // 0: set attribute
}

export interface ZengoBlockUsersRequest {
  FromUserId: string;
  UserIds: string[];
}

export interface ZengoUnblockUsersRequest {
  FromUserId: string;
  UserIds: string[];
}

export interface ZengoQueryBlocklistRequest {
  FromUserId: string;
  Limit: number;
  Next: number;
}

export interface ZengoQueryBlocklistResponse {
  Code: number;
  Message: string;
  RequestId: string;
  TotalCount?: number;
  Next?: number;
  Blacklist?: ZengoBlockedUser[];
}

export interface ZengoBlockedUser {
  UserId: string;
  UserName: string;
  Avatar: string;
  UpdateTime: number;
}

export interface ZengoCheckBlockshipRequest {
  FromUserId: string;
  UserIds: string[];
}

export interface ZengoCheckBlockshipResponse {
  Code: number;
  Message: string;
  RequestId: string;
  Succ?: ZengoBlockshipResult[];
  ErrList?: ZengoErrorItem[];
}

export interface ZengoBlockshipResult {
  UserId: string;
  Result: 0 | 1; // 0: not blocked, 1: blocked
}

export interface ViewStatusRequest {
  status_id: string;
}
