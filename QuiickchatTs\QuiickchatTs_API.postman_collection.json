{"info": {"name": "QuiickchatTs API", "description": "Complete API documentation for QuiickchatTs backend with JWT authentication", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "phone_number", "value": "+1234567890", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"password\": \"password123\",\n  \"name\": \"<PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}}, {"name": "Verify Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "        pm.collectionVariables.set('refresh_token', response.data.refresh_token);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-login"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "        pm.collectionVariables.set('refresh_token', response.data.refresh_token);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Resend Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-code", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-code"]}}}]}, {"name": "Users", "item": [{"name": "Initialize User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/users/init", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "init"]}}}, {"name": "Get Current User", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/users/me", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Name\",\n  \"bio\": \"Updated bio\",\n  \"avatar\": \"https://example.com/avatar.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/update-profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "update-profile"]}}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/users/upload-profile-picture", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "upload-profile-picture"]}}}, {"name": "Delete Account", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/users/delete-account", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "delete-account"]}}}]}, {"name": "Contacts", "item": [{"name": "Upload Contacts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"name\": \"Contact 1\",\n      \"phone\": \"+**********\"\n    },\n    {\n      \"name\": \"Contact 2\",\n      \"phone\": \"+**********\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/contacts/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "upload"]}}}, {"name": "Get Contacts", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/contacts", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts"]}}}, {"name": "Sync Contacts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"name\": \"Synced Contact\",\n      \"phone\": \"+**********\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/contacts/sync", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "sync"]}}}]}, {"name": "Status", "item": [{"name": "Create Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"My new status update!\",\n  \"type\": \"text\",\n  \"privacy\": \"contacts\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "status"]}}}, {"name": "Upload Status Media", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": []}, {"key": "caption", "value": "Status with media", "type": "text"}, {"key": "privacy", "value": "contacts", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}}}, {"name": "Get My Statuses", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/status/me", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "me"]}}}, {"name": "Get Contact Statuses", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/status/contacts", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "contacts"]}}}, {"name": "View Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status_id\": \"status_id_here\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/view", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "view"]}}}]}, {"name": "<PERSON> (Zengo)", "item": [{"name": "Get Service Status", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/status", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "status"]}}}, {"name": "Register Current User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"users\": [\n    {\n      \"user_id\": \"user123\",\n      \"nick\": \"<PERSON>\",\n      \"face_url\": \"https://example.com/avatar.jpg\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/register", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "register"]}}}, {"name": "Get User Online Status", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/users/online-status?user_ids=user123,user456", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "online-status"], "query": [{"key": "user_ids", "value": "user123,user456"}]}}}, {"name": "Get User Info", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/users/info?user_ids=user123,user456", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "info"], "query": [{"key": "user_ids", "value": "user123,user456"}]}}}, {"name": "Modify User Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"user123\",\n  \"nick\": \"Updated Name\",\n  \"face_url\": \"https://example.com/new-avatar.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/modify", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "modify"]}}}, {"name": "Add Friends", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"friend_list\": [\n    {\n      \"to_user_id\": \"user456\",\n      \"remark\": \"Best friend\",\n      \"add_source\": \"search\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/add", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "add"]}}}, {"name": "Send Friend Requests", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"req_msg\": \"Hi, let's be friends!\",\n  \"to_user_id_list\": [\"user456\", \"user789\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/request", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "request"]}}}, {"name": "Delete Friends", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"to_user_id_list\": [\"user456\"],\n  \"delete_type\": \"both\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/delete", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "delete"]}}}, {"name": "Delete All Friends", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"delete_type\": \"both\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/delete-all", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "delete-all"]}}}, {"name": "Query Friend List", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/friends/list?from_user_id=user123&start_index=0&standard_sequence=1&custom_sequence=0", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "list"], "query": [{"key": "from_user_id", "value": "user123"}, {"key": "start_index", "value": "0"}, {"key": "standard_sequence", "value": "1"}, {"key": "custom_sequence", "value": "0"}]}}}, {"name": "Update Friends Alias", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"friend_list\": [\n    {\n      \"to_user_id\": \"user456\",\n      \"remark\": \"New nickname\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/update-alias", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "update-alias"]}}}, {"name": "Update Friend Attributes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"to_user_id\": \"user456\",\n  \"ex\": \"custom_attribute_value\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/update-attributes", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "update-attributes"]}}}, {"name": "Block Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"to_user_id_list\": [\"user456\", \"user789\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/block", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "block"]}}}, {"name": "Unblock Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"to_user_id_list\": [\"user456\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/unblock", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "unblock"]}}}, {"name": "Query Blocklist", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/users/blocklist?from_user_id=user123&start_index=0", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "blocklist"], "query": [{"key": "from_user_id", "value": "user123"}, {"key": "start_index", "value": "0"}]}}}, {"name": "Check Blockship", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"from_user_id\": \"user123\",\n  \"to_user_id_list\": [\"user456\", \"user789\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/check-blockship", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "check-blockship"]}}}]}, {"name": "Health & Utilities", "item": [{"name": "Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}]}]}